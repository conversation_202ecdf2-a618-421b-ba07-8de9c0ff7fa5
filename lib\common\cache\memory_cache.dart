import 'dart:convert';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/constants/cache_constants.dart';

/// 内存缓存项
class MemoryCacheItem<T> {
  /// 缓存数据
  final T data;
  
  /// 过期时间（毫秒时间戳）
  final int expiryTime;
  
  /// 创建时间（毫秒时间戳）
  final int createTime;
  
  /// 构造函数
  MemoryCacheItem({
    required this.data,
    required this.expiryTime,
  }) : createTime = DateTime.now().millisecondsSinceEpoch;
  
  /// 判断缓存是否过期
  bool isExpired() {
    final now = DateTime.now().millisecondsSinceEpoch;
    return now >= expiryTime;
  }
  
  /// 获取剩余有效时间（毫秒）
  int getRemainingTime() {
    final now = DateTime.now().millisecondsSinceEpoch;
    final remaining = expiryTime - now;
    return remaining > 0 ? remaining : 0;
  }
  
  /// 获取已存在时间（毫秒）
  int getAge() {
    final now = DateTime.now().millisecondsSinceEpoch;
    return now - createTime;
  }
}

/// 内存缓存管理类
/// 
/// 提供高效的内存缓存实现，支持过期时间和自动清理
class MemoryCache {
  /// 缓存数据
  final Map<String, MemoryCacheItem> _cache = {};
  
  /// 默认过期时间（毫秒）
  int _defaultExpiry = CacheConstants.getMemoryExpiryMs(); // 使用常量定义默认过期时间
  
  /// 构造函数
  MemoryCache({int? defaultExpiry}) {
    if (defaultExpiry != null && defaultExpiry > 0) {
      _defaultExpiry = defaultExpiry;
    }
    
    // 启动定期清理任务
    _startCleanupTask();
  }
  
  /// 设置默认过期时间
  void setDefaultExpiry(int milliseconds) {
    if (milliseconds > 0) {
      _defaultExpiry = milliseconds;
    }
  }
  
  /// 获取缓存
  T? get<T>(
    String key, {
    int? maxAge,
    T? Function(Map<String, dynamic>)? fromJson,
  }) {
    // 检查缓存是否存在
    if (!_cache.containsKey(key)) {
      return null;
    }
    
    final item = _cache[key];
    if (item == null) {
      return null;
    }
    
    // 检查是否过期
    if (item.isExpired()) {
      _cache.remove(key);
      return null;
    }
    
    // 检查最大有效期
    if (maxAge != null) {
      final age = item.getAge();
      if (age > maxAge) {
        _cache.remove(key);
        return null;
      }
    }
    
    try {
      // 根据类型处理数据
      if (item.data is T) {
        return item.data as T;
      } 
      
      // 如果需要从JSON转换
      if (fromJson != null && item.data is Map<String, dynamic>) {
        return fromJson(item.data as Map<String, dynamic>);
      } else if (fromJson != null && item.data is String) {
        try {
          final map = jsonDecode(item.data as String) as Map<String, dynamic>;
          return fromJson(map);
        } catch (e) {
          LogUtil.error('内存缓存JSON解析失败: $key, 错误: $e');
          return null;
        }
      }
      
      // 尝试类型转换
      return item.data as T;
    } catch (e) {
      LogUtil.error('内存缓存类型转换失败: $key, 错误: $e');
      return null;
    }
  }
  
  /// 设置缓存
  Future<bool> set<T>(
    String key, 
    T data, {
    int? expiry,
    Map<String, dynamic> Function(T)? toJson,
  }) async {
    try {
      final expiryTime = DateTime.now().millisecondsSinceEpoch + (expiry ?? _defaultExpiry);
      
      _cache[key] = MemoryCacheItem(
        data: data,
        expiryTime: expiryTime,
      );
      
      return true;
    } catch (e) {
      LogUtil.error('内存缓存设置失败: $key, 错误: $e');
      return false;
    }
  }
  
  /// 删除缓存
  bool remove(String key) {
    if (_cache.containsKey(key)) {
      _cache.remove(key);
      return true;
    }
    return false;
  }
  
  /// 清空所有缓存
  bool clear() {
    _cache.clear();
    return true;
  }
  
  /// 检查缓存是否存在且未过期
  bool exists(String key) {
    if (!_cache.containsKey(key)) {
      return false;
    }
    
    final item = _cache[key];
    return item != null && !item.isExpired();
  }
  
  /// 获取缓存条目数量
  int size() {
    return _cache.length;
  }
  
  /// 获取所有缓存键
  List<String> getKeys() {
    return _cache.keys.toList();
  }
  
  /// 获取过期时间
  int? getExpiryTime(String key) {
    final item = _cache[key];
    if (item == null || item.isExpired()) {
      return null;
    }
    return item.expiryTime;
  }
  
  /// 清理过期缓存
  void _cleanupExpiredItems() {
    final now = DateTime.now().millisecondsSinceEpoch;
    final expiredKeys = _cache.keys.where((key) {
      final item = _cache[key];
      return item != null && item.expiryTime <= now;
    }).toList();
    
    for (final key in expiredKeys) {
      _cache.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      LogUtil.debug('内存缓存清理完成，移除了 ${expiredKeys.length} 个过期项');
    }
  }
  
  /// 启动定期清理任务
  void _startCleanupTask() {
    // 每分钟清理一次过期缓存
    Future.delayed(const Duration(minutes: 1), () {
      _cleanupExpiredItems();
      _startCleanupTask(); // 递归调用以实现定期清理
    });
  }
} 