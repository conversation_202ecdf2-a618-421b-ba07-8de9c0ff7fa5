import 'dart:async';
import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/manager/global_state.dart';

/// 事件优先级枚举
enum EventPriority { critical, high, normal, low }

/// 待处理事件数据类
class _PendingEvent {
  final String eventType;
  final Map<String, dynamic> data;
  final EventPriority priority;
  final DateTime timestamp;
  final String eventId;
  final Function() originalTrigger;

  _PendingEvent({
    required this.eventType,
    required this.data,
    required this.priority,
    required this.timestamp,
    required this.eventId,
    required this.originalTrigger,
  });
}

/// 事件监控数据类
class _EventMonitor {
  final String eventId;
  final DateTime startTime;
  final List<String> expectedHandlers;
  final Set<String> confirmedHandlers;

  _EventMonitor({
    required this.eventId,
    required this.startTime,
    required this.expectedHandlers,
    required this.confirmedHandlers,
  });
}

/// 事件统计数据类
class _EventStats {
  int totalEvents = 0;
  int duplicateEvents = 0;
  int timeoutEvents = 0;
  DateTime lastEventTime = DateTime.now();

  _EventStats();
}

/// 全局事件状态管理器
///
/// 使用GetX响应式变量管理全局事件状态，替代EventBus
/// 增强功能：去重、优先级、监控、统计
class GlobalEventState extends GetxController {
  static GlobalEventState get to => Get.find<GlobalEventState>();

  // ==================== 事件增强功能配置 ====================

  /// 是否启用事件增强功能
  bool _enhancementsEnabled = true;

  /// 事件去重缓存 - 按事件类型分组
  final Map<String, Set<String>> _eventDeduplicationCache = {};

  /// 事件优先级队列
  final Map<EventPriority, List<_PendingEvent>> _priorityQueues = {};

  /// 事件处理监控
  final Map<String, _EventMonitor> _eventMonitors = {};

  /// 事件处理统计
  final Map<String, _EventStats> _eventStats = {};

  /// 当前事件ID映射
  final Map<String, String> _currentEventIds = {};

  /// 配置参数
  static const int _maxCacheSize = 100;
  Duration _deduplicationWindow = const Duration(seconds: 5);
  Duration _eventTimeout = const Duration(seconds: 10);

  /// 事件ID计数器
  int _eventIdCounter = 0;

  /// 定期清理任务
  Timer? _cleanupTimer;

  @override
  void onInit() {
    super.onInit();
    _initializeEnhancements();
    LogUtil.info('GlobalEventState初始化完成');
  }

  @override
  void onReady() {
    super.onReady();
    LogUtil.debug('GlobalEventState准备就绪');
  }

  @override
  void onClose() {
    _cancelAllTimers();
    _cleanupEnhancements();
    super.onClose();
    LogUtil.info('GlobalEventState资源清理完成');
  }

  // ==================== 事件增强功能核心方法 ====================

  /// 初始化增强功能
  void _initializeEnhancements() {
    if (_enhancementsEnabled) {
      // 启动定期清理任务
      _cleanupTimer = Timer.periodic(const Duration(minutes: 2), (_) {
        _performPeriodicCleanup();
      });
      LogUtil.debug('事件增强功能已初始化');
    }
  }

  /// 清理增强功能资源
  void _cleanupEnhancements() {
    _cleanupTimer?.cancel();
    _eventDeduplicationCache.clear();
    _priorityQueues.clear();
    _eventMonitors.clear();
    _eventStats.clear();
    _currentEventIds.clear();
    LogUtil.debug('事件增强功能资源已清理');
  }

  /// 生成事件ID
  String _generateEventId() {
    return 'evt_${++_eventIdCounter}_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// 检查事件是否重复
  bool _isDuplicateEvent(String eventType, Map<String, dynamic> data) {
    if (!_enhancementsEnabled) return false;

    final eventKey = _generateEventKey(eventType, data);
    final cache = _eventDeduplicationCache[eventType] ??= <String>{};

    if (cache.contains(eventKey)) {
      _updateEventStats(eventType, isDuplicate: true);
      LogUtil.debug('检测到重复事件: $eventType, key: $eventKey');
      return true;
    }

    // 添加到缓存
    cache.add(eventKey);

    // 清理过期缓存
    _cleanupDeduplicationCache(eventType);

    return false;
  }

  /// 生成事件唯一键
  String _generateEventKey(String eventType, Map<String, dynamic> data) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    switch (eventType) {
      case 'websocketDisconnected':
        // WebSocket断开事件按秒级去重
        return '${eventType}_${timestamp ~/ 1000}';
      case 'aiReplyReset':
        // AI回复重置按角色和事件ID去重
        return '${eventType}_${data['roleId']}_${data['eventId']}';
      case 'messageAdded':
        // 消息添加按消息ID去重
        return '${eventType}_${data['messageId'] ?? data.hashCode}';
      case 'sessionUpdated':
        // 会话更新按会话ID和时间窗口去重
        return '${eventType}_${data['conversationId']}_${timestamp ~/ 2000}';
      default:
        // 默认按内容哈希和5秒时间窗口去重
        return '${eventType}_${data.hashCode}_${timestamp ~/ 5000}';
    }
  }

  /// 清理去重缓存
  void _cleanupDeduplicationCache(String eventType) {
    final cache = _eventDeduplicationCache[eventType];
    if (cache != null && cache.length > _maxCacheSize) {
      final list = cache.toList();
      cache.clear();
      // 保留后一半
      cache.addAll(list.skip(list.length ~/ 2));
      LogUtil.debug('已清理事件类型 $eventType 的去重缓存');
    }
  }

  /// 根据事件类型确定优先级
  EventPriority _getEventPriority(String eventType) {
    switch (eventType) {
      case 'websocketDisconnected':
      case 'websocketError':
      case 'tokenExpired':
        return EventPriority.critical;
      case 'aiReplyReset':
      case 'authProcessing':
        return EventPriority.high;
      case 'messageAdded':
      case 'sessionUpdated':
      case 'roleMessage':
        return EventPriority.normal;
      default:
        return EventPriority.low;
    }
  }

  /// 处理优先级事件
  void _processEventWithPriority(String eventType, Map<String, dynamic> data, Function() originalTrigger) {
    if (!_enhancementsEnabled) {
      originalTrigger();
      return;
    }

    final priority = _getEventPriority(eventType);

    if (priority == EventPriority.critical) {
      // 关键事件立即处理
      _executeEventWithMonitoring(eventType, data, originalTrigger);
    } else {
      // 其他事件进入队列
      _addToQueue(eventType, data, priority, originalTrigger);
      _processQueue();
    }
  }

  /// 添加事件到优先级队列
  void _addToQueue(String eventType, Map<String, dynamic> data, EventPriority priority, Function() originalTrigger) {
    final queue = _priorityQueues[priority] ??= [];
    final event = _PendingEvent(
      eventType: eventType,
      data: data,
      priority: priority,
      timestamp: DateTime.now(),
      eventId: _generateEventId(),
      originalTrigger: originalTrigger,
    );

    queue.add(event);

    // 限制队列大小
    if (queue.length > 50) {
      queue.removeAt(0);
      LogUtil.warn('事件队列 $priority 已满，移除最旧事件');
    }
  }

  /// 处理优先级队列
  void _processQueue() {
    for (final priority in EventPriority.values) {
      final queue = _priorityQueues[priority];
      if (queue != null && queue.isNotEmpty) {
        final event = queue.removeAt(0);
        _executeEventWithMonitoring(event.eventType, event.data, event.originalTrigger);
        break; // 一次只处理一个事件
      }
    }
  }

  /// 执行事件并监控
  void _executeEventWithMonitoring(String eventType, Map<String, dynamic> data, Function() originalTrigger) {
    final eventId = _generateEventId();

    // 记录当前事件ID
    _currentEventIds[eventType] = eventId;

    // 开始监控
    _startEventMonitoring(eventId, eventType);

    try {
      // 执行原始事件触发
      originalTrigger();

      // 更新统计
      _updateEventStats(eventType);

      // 设置超时检查
      _scheduleTimeoutCheck(eventId, eventType);

    } catch (e) {
      LogUtil.error('事件执行失败: $eventType, 错误: $e');
      _handleEventError(eventId, eventType, e);
    }
  }

  /// 开始事件监控
  void _startEventMonitoring(String eventId, String eventType) {
    if (!_enhancementsEnabled) return;

    _eventMonitors[eventId] = _EventMonitor(
      eventId: eventId,
      startTime: DateTime.now(),
      expectedHandlers: _getExpectedHandlers(eventType),
      confirmedHandlers: <String>{},
    );
  }

  /// 获取预期的事件处理器
  List<String> _getExpectedHandlers(String eventType) {
    switch (eventType) {
      case 'websocketDisconnected':
        return ['ChatService', 'WsManager'];
      case 'messageAdded':
        return ['MessageService', 'SessionService'];
      case 'sessionUpdated':
        return ['SessionService', 'SessionsController'];
      case 'aiReplyReset':
        return ['ChatService', 'AiChannelManager'];
      case 'roleMessage':
        return ['MessageService', 'ChatService'];
      default:
        return [];
    }
  }

  /// 确认事件处理
  void confirmEventHandled(String eventId, String handlerName) {
    if (!_enhancementsEnabled) return;

    final monitor = _eventMonitors[eventId];
    if (monitor != null) {
      monitor.confirmedHandlers.add(handlerName);

      LogUtil.debug('事件处理确认: $eventId by $handlerName');

      // 检查是否所有处理器都已确认
      if (monitor.confirmedHandlers.length >= monitor.expectedHandlers.length) {
        _completeEventMonitoring(eventId);
      }
    }
  }

  /// 获取当前事件ID
  String? _getCurrentEventId(String eventType) {
    return _currentEventIds[eventType];
  }

  /// 完成事件监控
  void _completeEventMonitoring(String eventId) {
    final monitor = _eventMonitors.remove(eventId);
    if (monitor != null) {
      final duration = DateTime.now().difference(monitor.startTime);
      LogUtil.debug('事件处理完成: $eventId, 耗时: ${duration.inMilliseconds}ms');

      // 清理当前事件ID映射
      _currentEventIds.removeWhere((key, value) => value == eventId);
    }
  }

  /// 设置超时检查
  void _scheduleTimeoutCheck(String eventId, String eventType) {
    Future.delayed(_eventTimeout, () {
      final monitor = _eventMonitors[eventId];
      if (monitor != null) {
        // 事件超时
        LogUtil.warn('事件处理超时: $eventType ($eventId)');
        _updateEventStats(eventType, isTimeout: true);
        _completeEventMonitoring(eventId);
      }
    });
  }

  /// 处理事件错误
  void _handleEventError(String eventId, String eventType, dynamic error) {
    LogUtil.error('事件处理错误: $eventType ($eventId), 错误: $error');
    _completeEventMonitoring(eventId);
  }

  /// 更新事件统计
  void _updateEventStats(String eventType, {bool isDuplicate = false, bool isTimeout = false}) {
    if (!_enhancementsEnabled) return;

    final stats = _eventStats[eventType] ??= _EventStats();

    if (isDuplicate) {
      stats.duplicateEvents++;
    } else if (isTimeout) {
      stats.timeoutEvents++;
    } else {
      stats.totalEvents++;
      stats.lastEventTime = DateTime.now();
    }
  }

  /// 定期清理任务
  void _performPeriodicCleanup() {
    if (!_enhancementsEnabled) return;

    final now = DateTime.now();

    // 清理过期的事件监控
    final expiredMonitors = <String>[];
    _eventMonitors.forEach((eventId, monitor) {
      if (now.difference(monitor.startTime) > _eventTimeout) {
        expiredMonitors.add(eventId);
      }
    });

    for (final eventId in expiredMonitors) {
      LogUtil.warn('清理过期事件监控: $eventId');
      _eventMonitors.remove(eventId);
    }

    // 清理去重缓存
    _eventDeduplicationCache.forEach((eventType, cache) {
      if (cache.length > _maxCacheSize) {
        _cleanupDeduplicationCache(eventType);
      }
    });

    // 清理优先级队列中的过期事件
    _priorityQueues.forEach((priority, queue) {
      final expiredCount = queue.length;
      queue.removeWhere((event) {
        return now.difference(event.timestamp) > const Duration(minutes: 5);
      });

      if (queue.length < expiredCount) {
        LogUtil.debug('清理了 ${expiredCount - queue.length} 个过期的 $priority 优先级事件');
      }
    });

    // 清理当前事件ID映射中的过期项
    _currentEventIds.removeWhere((eventType, eventId) {
      return !_eventMonitors.containsKey(eventId);
    });

    LogUtil.debug('定期清理完成 - 监控器: ${_eventMonitors.length}, 缓存: ${_eventDeduplicationCache.length}');
  }

  /// 获取事件统计信息
  Map<String, dynamic> getEventStatistics() {
    return {
      'enhancementsEnabled': _enhancementsEnabled,
      'totalEventTypes': _eventStats.length,
      'eventStats': _eventStats.map((type, stats) => MapEntry(type, {
        'total': stats.totalEvents,
        'duplicates': stats.duplicateEvents,
        'timeouts': stats.timeoutEvents,
        'lastEvent': stats.lastEventTime.toIso8601String(),
      })),
      'activeMonitors': _eventMonitors.length,
      'queueSizes': _priorityQueues.map((priority, queue) => MapEntry(priority.toString(), queue.length)),
      'cacheStats': _eventDeduplicationCache.map((type, cache) => MapEntry(type, cache.length)),
    };
  }

  // ==================== 配置和控制方法 ====================

  /// 启用/禁用事件增强功能
  void setEventEnhancementsEnabled(bool enabled) {
    _enhancementsEnabled = enabled;
    LogUtil.info('事件增强功能${enabled ? '已启用' : '已禁用'}');

    if (!enabled) {
      // 禁用时清理资源
      _cleanupEnhancements();
    } else {
      // 启用时重新初始化
      _initializeEnhancements();
    }
  }

  /// 配置去重参数
  void configureDeduplication({
    Duration? window,
    int? maxCacheSize,
  }) {
    if (window != null) {
      _deduplicationWindow = window;
      LogUtil.debug('去重时间窗口已设置为: ${window.inSeconds}秒');
    }
    if (maxCacheSize != null) {
      // 更新最大缓存大小需要重新定义常量，这里通过清理实现
      LogUtil.debug('最大缓存大小配置: $maxCacheSize');
    }
  }

  /// 配置事件超时
  void configureEventTimeout(Duration timeout) {
    _eventTimeout = timeout;
    LogUtil.debug('事件超时时间已设置为: ${timeout.inSeconds}秒');
  }

  /// 手动触发清理
  void manualCleanup() {
    _performPeriodicCleanup();
    LogUtil.info('手动清理完成');
  }

  /// 重置所有统计
  void resetStatistics() {
    _eventStats.clear();
    LogUtil.info('事件统计已重置');
  }

  /// 获取当前配置
  Map<String, dynamic> getCurrentConfiguration() {
    return {
      'enhancementsEnabled': _enhancementsEnabled,
      'deduplicationWindow': _deduplicationWindow.inSeconds,
      'eventTimeout': _eventTimeout.inSeconds,
      'maxCacheSize': _maxCacheSize,
    };
  }

  // ==================== WebSocket相关事件状态 ====================

  /// WebSocket断开连接事件（保留为事件，因为需要传递错误信息）
  final RxBool websocketDisconnected = false.obs;
  
  /// AI回复重置事件
  final Rx<Map<String, dynamic>?> aiReplyReset = Rx<Map<String, dynamic>?>(null);
  
  /// WebSocket错误信息
  final Rx<Map<String, dynamic>?> websocketError = Rx<Map<String, dynamic>?>(null);

  // ==================== 消息相关事件状态 ====================
  
  /// 消息添加事件
  final Rx<Map<String, dynamic>?> messageAdded = Rx<Map<String, dynamic>?>(null);
  
  /// 消息更新事件
  final Rx<Map<String, dynamic>?> messageUpdated = Rx<Map<String, dynamic>?>(null);
  
  /// 消息删除事件
  final Rx<Map<String, dynamic>?> messageDeleted = Rx<Map<String, dynamic>?>(null);

  // ==================== 会话相关事件状态 ====================
  
  /// 会话更新事件
  final Rx<Map<String, dynamic>?> sessionUpdated = Rx<Map<String, dynamic>?>(null);
  
  /// 会话创建事件
  final Rx<Map<String, dynamic>?> sessionCreated = Rx<Map<String, dynamic>?>(null);
  
  /// 会话删除事件
  final Rx<Map<String, dynamic>?> sessionDeleted = Rx<Map<String, dynamic>?>(null);

  // ==================== 角色相关事件状态 ====================
  
  /// 角色消息事件
  final Rx<Map<String, dynamic>?> roleMessage = Rx<Map<String, dynamic>?>(null);
  
  /// 活跃角色变更事件
  final Rx<Map<String, dynamic>?> activeRoleChanged = Rx<Map<String, dynamic>?>(null);
  
  /// 角色会话绑定事件
  final Rx<Map<String, dynamic>?> roleConversationBound = Rx<Map<String, dynamic>?>(null);

  /// 用户身份变更事件
  final Rx<Map<String, dynamic>?> userIdentityChanged = Rx<Map<String, dynamic>?>(null);

  // 数据刷新事件已迁移到GlobalState统一管理

  // ==================== 资源清理事件状态 ====================
  
  /// 角色资源清理事件
  final Rx<Map<String, dynamic>?> roleResourceCleanup = Rx<Map<String, dynamic>?>(null);

  /// 角色绑定清理事件
  final Rx<Map<String, dynamic>?> roleBindingCleanup = Rx<Map<String, dynamic>?>(null);

  /// 会话资源清理事件
  final Rx<Map<String, dynamic>?> conversationResourceCleanup = Rx<Map<String, dynamic>?>(null);

  /// 会话缓存清理事件
  final Rx<Map<String, dynamic>?> sessionCacheCleanup = Rx<Map<String, dynamic>?>(null);

  /// 取消所有活跃的Timer - 已移除，使用Future.delayed替代
  void _cancelAllTimers() {
    // 不再需要取消Timer，Future.delayed会自动完成
    LogUtil.debug('Timer管理已移除，使用Future.delayed替代');
  }

  /// 取消特定事件的自动重置
  void cancelAutoReset(String eventType) {
    // 这里可以根据需要实现特定事件的取消逻辑
    LogUtil.debug('取消事件自动重置: $eventType');
  }

  // ==================== 认证相关事件触发方法（已迁移到GlobalState） ====================

  /// 触发Token过期事件 - 重定向到GlobalState
  @Deprecated('使用GlobalState.to.setTokenExpired()替代')
  void triggerTokenExpired({
    String? reason,
    Duration? autoResetDelay = const Duration(milliseconds: 100),
  }) {
    // 重定向到GlobalState
    try {
      final globalState = Get.find<GlobalState>();
      globalState.setTokenExpired(true);
      LogUtil.info('触发Token过期事件${reason != null ? ', 原因: $reason' : ''} (已重定向到GlobalState)');
    } catch (e) {
      LogUtil.error('重定向Token过期事件到GlobalState失败: $e');
    }
  }

  /// 触发认证流程开始 - 重定向到GlobalState
  @Deprecated('使用GlobalState.to.startAuthProcess()替代')
  void triggerAuthProcessStarted({String? userId}) {
    try {
      final globalState = Get.find<GlobalState>();
      globalState.startAuthProcess();
      LogUtil.info('触发认证流程开始事件${userId != null ? ', 用户ID: $userId' : ''} (已重定向到GlobalState)');
    } catch (e) {
      LogUtil.error('重定向认证开始事件到GlobalState失败: $e');
    }
  }

  /// 触发认证流程完成 - 重定向到GlobalState
  @Deprecated('使用GlobalState.to.completeAuthProcess()替代')
  void triggerAuthProcessCompleted({String? userId}) {
    try {
      final globalState = Get.find<GlobalState>();
      globalState.completeAuthProcess();
      LogUtil.info('触发认证流程完成事件${userId != null ? ', 用户ID: $userId' : ''} (已重定向到GlobalState)');
    } catch (e) {
      LogUtil.error('重定向认证完成事件到GlobalState失败: $e');
    }
  }

  /// 触发Token验证 - 重定向到GlobalState
  @Deprecated('使用GlobalState.to.setTokenValidated()替代')
  void triggerTokenValidated({
    String? userId,
    int? tokenLength,
    Duration? autoResetDelay = const Duration(milliseconds: 100),
  }) {
    try {
      final globalState = Get.find<GlobalState>();
      globalState.setTokenValidated(true);
      LogUtil.info('触发Token验证事件${userId != null ? ', 用户ID: $userId' : ''} (已重定向到GlobalState)');
    } catch (e) {
      LogUtil.error('重定向Token验证事件到GlobalState失败: $e');
    }
  }

  // ==================== WebSocket相关事件触发方法 ====================
  
  /// 触发WebSocket断开连接事件
  void triggerWebsocketDisconnected({
    String? error,
    String? state,
    Duration? autoResetDelay = const Duration(milliseconds: 500),
  }) {
    websocketDisconnected.value = true;
    LogUtil.info('触发WebSocket断开连接事件${error != null ? ', 错误: $error' : ''}${state != null ? ', 状态: $state' : ''}');

    // 可配置的自动重置状态 - 使用Future.delayed替代Timer
    if (autoResetDelay != null) {
      Future.delayed(autoResetDelay, () {
        websocketDisconnected.value = false;
      });
    }
  }
  
  /// 触发AI回复重置事件
  void triggerAiReplyReset({
    String? eventId,
    int? roleId,
    Duration? autoResetDelay = const Duration(milliseconds: 300),
  }) {
    aiReplyReset.value = {
      'eventId': eventId,
      'roleId': roleId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.info('触发AI回复重置事件${eventId != null ? ', 事件ID: $eventId' : ''}${roleId != null ? ', 角色ID: $roleId' : ''}');

    // 可配置的自动重置状态 - 使用Future.delayed替代Timer
    if (autoResetDelay != null) {
      Future.delayed(autoResetDelay, () {
        aiReplyReset.value = null;
      });
    }
  }
  
  /// 触发WebSocket错误事件
  void triggerWebsocketError(Map<String, dynamic> errorData) {
    websocketError.value = errorData;
    LogUtil.info('触发WebSocket错误事件: ${errorData['errorType'] ?? 'UNKNOWN'}');
  }

  // ==================== 消息相关事件触发方法 ====================
  
  /// 触发消息添加事件
  void triggerMessageAdded(Map<String, dynamic> messageData) {
    messageAdded.value = messageData;
    LogUtil.debug('触发消息添加事件');
  }
  
  /// 触发消息更新事件
  void triggerMessageUpdated(Map<String, dynamic> messageData) {
    messageUpdated.value = messageData;
    LogUtil.debug('触发消息更新事件');
  }

  // ==================== 会话相关事件触发方法 ====================
  
  /// 触发会话更新事件
  void triggerSessionUpdated(int conversationId, String? lastMessage, DateTime? lastMessageTime) {
    sessionUpdated.value = {
      'conversationId': conversationId,
      'lastMessage': lastMessage,
      'lastMessageTime': lastMessageTime,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发会话更新事件: conversationId=$conversationId');
  }

  // ==================== 角色相关事件触发方法 ====================
  
  /// 触发角色消息事件
  void triggerRoleMessage(Map<String, dynamic> messageData) {
    roleMessage.value = messageData;
    LogUtil.debug('触发角色消息事件: roleId=${messageData['roleId']}');
  }

  /// 触发活跃角色变更事件
  void triggerActiveRoleChanged(
    int roleId,
    int? previousRoleId, {
    Duration? autoResetDelay = const Duration(milliseconds: 300),
  }) {
    activeRoleChanged.value = {
      'roleId': roleId,
      'previousRoleId': previousRoleId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发活跃角色变更事件: roleId=$roleId, previousRoleId=$previousRoleId');

    // 可配置的自动重置状态 - 使用Future.delayed替代Timer
    if (autoResetDelay != null) {
      Future.delayed(autoResetDelay, () {
        activeRoleChanged.value = null;
      });
    }
  }

  /// 触发角色会话绑定事件
  void triggerRoleConversationBound(int roleId, int conversationId) {
    roleConversationBound.value = {
      'role_id': roleId,
      'conversation_id': conversationId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发角色会话绑定事件: roleId=$roleId, conversationId=$conversationId');
  }

  // ==================== 数据刷新事件触发方法（已迁移到GlobalState） ====================

  /// 触发推荐数据刷新 - 重定向到GlobalState
  @Deprecated('使用GlobalState.to.triggerRefreshRecommendData()替代')
  void triggerRefreshRecommendData({bool forceRefresh = true, String? source}) {
    try {
      final globalState = Get.find<GlobalState>();
      globalState.triggerRefreshRecommendData();
      LogUtil.info('触发推荐数据刷新${source != null ? ', 来源: $source' : ''} (已重定向到GlobalState)');
    } catch (e) {
      LogUtil.error('重定向推荐数据刷新事件到GlobalState失败: $e');
    }
  }

  /// 触发会话数据刷新 - 重定向到GlobalState
  @Deprecated('使用GlobalState.to.triggerRefreshSessionsData()替代')
  void triggerRefreshSessionsData({bool forceRefresh = true, String? source}) {
    try {
      final globalState = Get.find<GlobalState>();
      globalState.triggerRefreshSessionsData();
      LogUtil.info('触发会话数据刷新${source != null ? ', 来源: $source' : ''} (已重定向到GlobalState)');
    } catch (e) {
      LogUtil.error('重定向会话数据刷新事件到GlobalState失败: $e');
    }
  }

  /// 触发用户身份变更事件
  void triggerUserIdentityChanged({
    String? userId,
    Duration? autoResetDelay = const Duration(milliseconds: 500),
  }) {
    userIdentityChanged.value = {
      'userId': userId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.info('触发用户身份变更事件${userId != null ? ', userId: $userId' : ''}');

    // 可配置的自动重置状态 - 使用Future.delayed替代Timer
    if (autoResetDelay != null) {
      Future.delayed(autoResetDelay, () {
        userIdentityChanged.value = null;
      });
    }
  }

  // ==================== 资源清理事件触发方法 ====================
  
  /// 触发角色资源清理事件
  void triggerRoleResourceCleanup(int roleId, {int? conversationId, String? reason}) {
    roleResourceCleanup.value = {
      'roleId': roleId,
      'conversationId': conversationId,
      'reason': reason,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发角色资源清理事件: roleId=$roleId${reason != null ? ', 原因: $reason' : ''}');
  }

  /// 触发角色绑定清理事件
  void triggerRoleBindingCleanup(int roleId, {int? conversationId}) {
    roleBindingCleanup.value = {
      'roleId': roleId,
      'conversationId': conversationId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发角色绑定清理事件: roleId=$roleId, conversationId=$conversationId');
  }

  /// 触发会话资源清理事件
  void triggerConversationResourceCleanup(int conversationId, {String? reason}) {
    conversationResourceCleanup.value = {
      'conversationId': conversationId,
      'reason': reason,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发会话资源清理事件: conversationId=$conversationId${reason != null ? ', 原因: $reason' : ''}');
  }

  /// 触发会话缓存清理事件
  void triggerSessionCacheCleanup(int conversationId, {String? reason}) {
    sessionCacheCleanup.value = {
      'conversationId': conversationId,
      'reason': reason,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发会话缓存清理事件: conversationId=$conversationId${reason != null ? ', 原因: $reason' : ''}');
  }
}
