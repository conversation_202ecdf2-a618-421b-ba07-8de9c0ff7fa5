import 'dart:io';
import 'package:rolio/common/utils/logger.dart';

class Env {
  // 获取到当前环境
  static const appEnv = String.fromEnvironment(EnvName.envKey, defaultValue: EnvName.debug);

  //http://10.11.15.243:8050/
  // 开发环境  flutter build apk --debug --dart-define=APP_ENV=debug
  static final EnvConfig _debugConfig = EnvConfig(
    serverAddr: "10.11.15.243:8051/",  // WebSocket服务地址
    accountServiceUrl: "http://10.11.15.243:8050",  // 账号服务地址
    aiServiceUrl: "http://10.11.15.243:8050/api",  // AI服务地址
    appID: Platform.isIOS ? 0 : 0,
    wsProtocol: "ws", // 测试环境使用非加密WebSocket
  );

  // 测试环境  flutter build apk --debug --dart-define=APP_ENV=test
  static final EnvConfig _testConfig = EnvConfig(
    serverAddr: "10.0.1.84:50501/",  // WebSocket服务地址
    accountServiceUrl: "http://10.0.1.84:50500/",  // 账号服务地址
    aiServiceUrl: "http://10.0.1.84:50500/api",  // AI服务地址
    appID: Platform.isIOS ? 0 : 0,
    wsProtocol: "ws", // 测试环境使用非加密WebSocket
  );

  // 发布环境  flutter build apk --build-name=版本号 --dart-define=APP_ENV=release
  static final EnvConfig _releaseConfig = EnvConfig(
    serverAddr: "chat.rolio-ai.com",  // WebSocket服务地址，不添加端口号和末尾斜杠
    accountServiceUrl: "https://api.rolio-ai.com",  // 账号服务地址
    aiServiceUrl: "https://api.rolio-ai.com/api",  // AI服务地址
    appID: Platform.isIOS ? 0 : 0,
    wsProtocol: "wss", // 生产环境使用加密WebSocket
  );
  static EnvConfig get envConfig => _getEnvConfig();

  static EnvConfig _getEnvConfig() {
    final config = _getEnvConfigInternal();
    LogUtil.info('环境配置: 当前环境=$appEnv');
    LogUtil.debug('API服务地址: ${config.aiServiceUrl}');
    LogUtil.debug('账号服务地址: ${config.accountServiceUrl}');
    
    // 检查WebSocket服务器地址配置
    if (config.serverAddr.isEmpty) {
      LogUtil.error('错误: WebSocket服务器地址为空！');
    } else if (config.serverAddr.contains(" ")) {
      LogUtil.error('错误: WebSocket服务器地址包含空格: "${config.serverAddr}"');
    } else if (config.serverAddr.startsWith("ws://") || config.serverAddr.startsWith("wss://")) {
      // 如果已经包含WebSocket协议前缀，直接使用
      LogUtil.info('WebSocket地址(已包含协议): ${config.serverAddr}');
    } else {
      // 判断协议类型
      String protocol = "ws://";
      if (!_isIpAddress(config.serverAddr) && 
          (config.serverAddr.contains(".com") || config.serverAddr.contains(".org") || 
           config.serverAddr.contains(".net") || config.serverAddr.contains(".cn"))) {
        protocol = "wss://";
      }
      LogUtil.info('WebSocket地址: $protocol${config.serverAddr}');
    }
    
    return config;
  }
  
  static EnvConfig _getEnvConfigInternal() {
    switch (appEnv) {
      case EnvName.debug:
        return _debugConfig;
      case EnvName.release:
        return _releaseConfig;
      case EnvName.test:
        return _testConfig;
      default:
        return _debugConfig;
    }
  }

  // 检查字符串是否为IP地址
  static bool _isIpAddress(String host) {
    // 简单检查是否为IP地址格式
    String hostWithoutPort = host.split(':')[0];
    RegExp ipRegex = RegExp(r'^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$');
    return ipRegex.hasMatch(hostWithoutPort);
  }
}

class EnvConfig {
  final String serverAddr;
  final String accountServiceUrl;
  final String aiServiceUrl;
  final int appID;
  final String? wsProtocol; // WebSocket协议，可以是"ws"或"wss"

  EnvConfig({
    required this.serverAddr,
    required this.accountServiceUrl,
    required this.aiServiceUrl,
    required this.appID,
    this.wsProtocol, // 可选参数，允许为null
  });
}

abstract class EnvName {
  // 环境key
  static const String envKey = "APP_ENV";

  // 环境value
  static const String debug = "debug";
  static const String test = "test";
  static const String release = "release";
}
